#!/usr/bin/env python3
"""
Convert C4 Markdown documentation to HTML with embedded Mermaid diagrams.
This HTML can then be converted to PDF using a browser or other tools.
"""

import re
import os


def read_file(filename):
    """Read file content."""
    with open(filename, 'r', encoding='utf-8') as f:
        return f.read()


def create_html_with_mermaid(markdown_content):
    """Convert markdown to HTML with Mermaid support."""

    # Convert markdown to HTML
    html_content = markdown_content

    # Convert mermaid code blocks FIRST (before other conversions)
    def replace_mermaid(match):
        mermaid_code = match.group(1).strip()
        return f'<div class="mermaid">\n{mermaid_code}\n</div>'

    html_content = re.sub(r'```mermaid\n(.*?)\n```',
                          replace_mermaid, html_content, flags=re.DOTALL)

    # Convert headers
    html_content = re.sub(r'^# (.+)$', r'<h1>\1</h1>',
                          html_content, flags=re.MULTILINE)
    html_content = re.sub(
        r'^## (.+)$', r'<h2 class="page-break">\1</h2>', html_content, flags=re.MULTILINE)
    html_content = re.sub(r'^### (.+)$', r'<h3>\1</h3>',
                          html_content, flags=re.MULTILINE)
    html_content = re.sub(r'^#### (.+)$', r'<h4>\1</h4>',
                          html_content, flags=re.MULTILINE)

    # Convert bold text
    html_content = re.sub(
        r'\*\*(.+?)\*\*', r'<strong>\1</strong>', html_content)

    # Convert inline code
    html_content = re.sub(r'`([^`]+)`', r'<code>\1</code>', html_content)

    # Convert mermaid code blocks
    def replace_mermaid(match):
        mermaid_code = match.group(1).strip()
        return f'<div class="mermaid">\n{mermaid_code}\n</div>'

    html_content = re.sub(r'```mermaid\n(.*?)\n```',
                          replace_mermaid, html_content, flags=re.DOTALL)

    # Convert tables
    def convert_table(match):
        table_content = match.group(0)
        lines = table_content.strip().split('\n')

        if len(lines) < 2:
            return table_content

        # Header row
        # Remove empty first and last elements
        header = lines[0].split('|')[1:-1]
        header = [cell.strip() for cell in header]

        # Skip separator row (lines[1])

        # Data rows
        data_rows = []
        for line in lines[2:]:
            if '|' in line:
                # Remove empty first and last elements
                row = line.split('|')[1:-1]
                row = [cell.strip() for cell in row]
                data_rows.append(row)

        # Build HTML table
        html_table = '<table>\n<thead>\n<tr>\n'
        for cell in header:
            html_table += f'<th>{cell}</th>\n'
        html_table += '</tr>\n</thead>\n<tbody>\n'

        for row in data_rows:
            html_table += '<tr>\n'
            for cell in row:
                html_table += f'<td>{cell}</td>\n'
            html_table += '</tr>\n'

        html_table += '</tbody>\n</table>'
        return html_table

    # Find and convert tables
    table_pattern = r'\|.*\|\n\|[-\s\|:]+\|\n(?:\|.*\|\n)+'
    html_content = re.sub(table_pattern, convert_table,
                          html_content, flags=re.MULTILINE)

    # Convert lists
    html_content = re.sub(
        r'^(\d+)\. (.+)$', r'<ol><li>\2</li></ol>', html_content, flags=re.MULTILINE)
    html_content = re.sub(r'^- (.+)$', r'<ul><li>\1</li></ul>',
                          html_content, flags=re.MULTILINE)

    # Fix consecutive list items
    html_content = re.sub(r'</ol>\n<ol>', '', html_content)
    html_content = re.sub(r'</ul>\n<ul>', '', html_content)

    # Convert horizontal rules
    html_content = re.sub(r'^---$', r'<hr>', html_content, flags=re.MULTILINE)

    # Convert paragraphs
    paragraphs = html_content.split('\n\n')
    html_paragraphs = []

    for para in paragraphs:
        para = para.strip()
        if para and not para.startswith('<'):
            # Only wrap in <p> if it's not already HTML
            if not any(para.startswith(tag) for tag in ['<h', '<div', '<table', '<ul', '<ol', '<hr']):
                para = f'<p>{para}</p>'
        html_paragraphs.append(para)

    html_content = '\n\n'.join(html_paragraphs)

    # HTML template with Mermaid.js support
    html_template = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Eye API - C4 Architecture Documentation</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 5px;
            margin-top: 30px;
        }
        h3 {
            color: #2c3e50;
            margin-top: 25px;
        }
        h4 {
            color: #34495e;
            margin-top: 20px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .mermaid {
            text-align: center;
            margin: 20px 0;
        }
        code {
            background-color: #f4f4f4;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        ul, ol {
            margin: 10px 0;
            padding-left: 30px;
        }
        li {
            margin: 5px 0;
        }
        .page-break {
            page-break-before: always;
        }
        @media print {
            body {
                font-size: 12pt;
                line-height: 1.4;
            }
            h1 {
                font-size: 18pt;
            }
            h2 {
                font-size: 16pt;
            }
            h3 {
                font-size: 14pt;
            }
            .mermaid {
                break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            },
            c4: {
                useMaxWidth: true
            }
        });
    </script>

{content}

</body>
</html>"""

    return html_template.replace('{content}', html_content)


def main():
    """Main function to convert markdown to HTML."""
    input_file = 'c4-pdf-ready.md'
    output_file = 'c4-architecture-documentation.html'

    if not os.path.exists(input_file):
        print(f"Error: {input_file} not found!")
        return

    print(f"Reading {input_file}...")
    markdown_content = read_file(input_file)

    print("Converting to HTML with Mermaid support...")
    html_content = create_html_with_mermaid(markdown_content)

    print(f"Writing {output_file}...")
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)

    print(f"✅ Conversion complete!")
    print(f"📄 HTML file created: {output_file}")
    print("\n📋 To convert to PDF:")
    print("1. Open the HTML file in Chrome/Edge")
    print("2. Press Ctrl+P (or Cmd+P on Mac)")
    print("3. Choose 'Save as PDF' as destination")
    print("4. Set margins to 'Minimum' for better layout")
    print("5. Enable 'Background graphics' for better styling")
    print("6. Click 'Save'")


if __name__ == "__main__":
    main()
